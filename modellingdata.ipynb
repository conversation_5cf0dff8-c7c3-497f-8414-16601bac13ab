# Import library yang diperlukan
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
plt.style.use('default')
plt.rcParams['figure.figsize'] = (10, 6)
print("Library berhasil diimport")

# Buat data sederhana
jam_belajar = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
nilai_ujian = np.array([50, 55, 60, 65, 70, 75, 80, 85, 90, 95])

# Buat DataFrame
data = pd.DataFrame({'jam_belajar': jam_belajar,
                     'nilai_ujian': nilai_ujian})
print(data)

# Visualisasi data
plt.scatter(data['jam_belajar'], data['nilai_ujian'], color='blue', s=100, alpha=0.7)
plt.xlabel('Jam Belajar')
plt.ylabel('<PERSON><PERSON>')
plt.title('Hubungan Jam Belajar vs <PERSON><PERSON>')
plt.grid(True, alpha=0.3)
plt.show()
print("Terdapat data: semakin tinggi jam belajar, nilai semakin tinggi")

# Pisahkan data input x (input) dan y (output)
x = data[['jam_belajar']]  # Input: jam belajar
y = data['nilai_ujian']    # Output: nilai ujian

# Buat model linear regression
model = LinearRegression()

# Training model
model.fit(x, y)

# Ambil hasil training
slope = model.coef_[0]     # Kemiringan garis
intercept = model.intercept_  # Titik potong

print("Model berhasil ditraining!")
print(f"Koefisien: nilai_ujian = {slope:.2f} * jam_belajar + {intercept:.2f}")

# Visualisasi data
plt.scatter(data['jam_belajar'], data['nilai_ujian'], color='blue', s=100, alpha=0.7, label='Data Asli')

# Buat garis regresi
x_line = np.linspace(0, 10, 100)
y_line = slope * x_line + intercept
plt.plot(x_line, y_line, color='red', linewidth=3, label='Garis Regresi')

plt.xlabel('Jam Belajar')
plt.ylabel('Nilai Ujian')
plt.title('Model Linear Regression: Jam Belajar vs Nilai Ujian')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("Interpretasi: Dengan 1 jam belajar tambahan, nilai naik {slope:.2f} poin".format(slope=slope))

# Prediksi nilai menggunakan model
y_predik = model.predict(x)

# Hitung mean squared error
mse = mean_squared_error(y, y_predik)
r2 = model.score(x, y)  # R-squared

print("Mean Squared Error (MSE): {:.2f}".format(mse))
print("R-squared (R²): {:.2f}".format(r2))

# Residual = actual - predicted
residual = y - y_predik

plt.scatter(y_predik, residual, color='green', alpha=0.7)
plt.axhline(y=0, color='red', linestyle='--')
plt.xlabel('Prediksi Nilai')
plt.ylabel('Residual')
plt.title('Residual Plot')
plt.show()

print("Non-Bias: residual rata-rata 0, model tidak bias")
print("Hampir sempurna: R² = 1.0, model sangat bagus")

# Contoh prediksi untuk jam belajar = 6 jam
predik_nilai = model.predict([[6]])[0]
print(f"Prediksi nilai untuk 6 jam belajar: {predik_nilai:.2f}")

# Data test sederhana
jam_test = np.array([2.5, 4.5, 7.5])
nilai_test = np.array([58, 72, 85])  # Asumsi actual untuk test

x_test = pd.DataFrame({'jam_belajar': jam_test})
y_test = nilai_test

predik_test = model.predict(x_test)

print("Prediksi untuk test data:")
for i in range(len(jam_test)):
    print(f"Jam {jam_test[i]}: Prediksi = {predik_test[i]:.2f}, Actual = {y_test[i]:.2f}")

# Visualisasi train
plt.scatter(data['jam_belajar'], data['nilai_ujian'], color='blue', s=100, alpha=0.7, label='Data Train')

# Visualisasi test
plt.scatter(jam_test, y_test, color='green', s=100, alpha=0.7, marker='x', label='Data Test')
plt.scatter(jam_test, predik_test, color='red', s=100, alpha=0.7, marker='o', label='Prediksi Test')

# Garis regresi
x_line = np.linspace(0, 10, 100)
y_line = slope * x_line + intercept
plt.plot(x_line, y_line, color='red', linewidth=2, label='Garis Regresi')

plt.xlabel('Jam Belajar')
plt.ylabel('Nilai Ujian')
plt.title('Prediksi dengan Linear Regression')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("Prediksi untuk 6 jam belajar: 76. belajar")
print("Nilai ujian diprediksi 76. belajar: 2.5 jam = 64.71, 4.5 jam = 74. belajar: 4.5 jam = 74.71")
print("5 jam = 84.71")

# Import dataset California Housing
from sklearn.datasets import fetch_california_housing
california = fetch_california_housing()

# Buat DataFrame
df_california = pd.DataFrame(california.data, columns=california.feature_names)
df_california['PRICE'] = california.target  # Harga rumah (dalam $100k)

print("Dataset California Housing:")
print(df_california.head())

# Ambil 1 fitur: MedInc (median income)
x_price = df_california[['MedInc']]  # Input: Median Income
y_price = df_california['PRICE']     # Output: Harga Rumah

# Buat model
model_house = LinearRegression()

# Training
model_house.fit(x_price, y_price)

# Hasil
slope_house = model_house.coef_[0]
intercept_house = model_house.intercept_

print("Model house berhasil training!")
print(f"Slope house: {slope_house:.2f}")
print(f"Intercept house: {intercept_house:.2f}")

# Visualisasi
plt.scatter(x_price, y_price, color='green', alpha=0.5, label='Data')
x_line_house = np.linspace(0, 10, 100)
y_line_house = slope_house * x_line_house + intercept_house
plt.plot(x_line_house, y_line_house, color='red', linewidth=2, label='Regresi')

plt.xlabel('Median Income (dalam $10k)')
plt.ylabel('Harga Rumah (dalam $100k)')
plt.title('Prediksi Harga Rumah berdasarkan Median Income')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("Setup: 1 unit median income = harga rumah naik ${slope_house*10:.0f}k".format(slope_house=slope_house))

# Prediksi
y_pred_house = model_house.predict(x_price)

# MSE dan R²
mse_house = mean_squared_error(y_price, y_pred_house)
r2_house = model_house.score(x_price, y_price)

print("Mean Squared Error (MSE): {:.2f}".format(mse_house))
print("R-squared (R²): {:.2f}".format(r2_house))
print("Residual rate error: (sqrt(MSE) / mean harga) * 100 ~ kurang lebih 50% (data noisy)")
print("~ Perfect prediction 0.0, model tidak berguna")

residual_house = y_price - y_pred_house

plt.figure(figsize=(10, 6))
plt.hist(residual_house, bins=50, color='purple', alpha=0.7)
plt.xlabel('Residual')
plt.ylabel('Frekuensi')
plt.title('Histogram Residual Harga Rumah')
plt.axvline(0, color='red', linestyle='--')
plt.show()